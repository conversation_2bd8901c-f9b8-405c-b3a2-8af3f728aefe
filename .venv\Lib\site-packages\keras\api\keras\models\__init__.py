# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator/create_python_api.py script.
"""Keras models API.
"""

import sys as _sys

from keras.engine.sequential import Sequential
from keras.engine.training import Model
from keras.models.cloning import clone_model
from keras.premade_models.linear import LinearModel
from keras.premade_models.wide_deep import WideDeepModel
from keras.saving.legacy.model_config import model_from_config
from keras.saving.legacy.model_config import model_from_json
from keras.saving.legacy.model_config import model_from_yaml
from keras.saving.legacy.save import load_model
from keras.saving.legacy.save import save_model
from tensorflow.python.util import module_wrapper as _module_wrapper

if not isinstance(_sys.modules[__name__], _module_wrapper.TFModuleWrapper):
  _sys.modules[__name__] = _module_wrapper.TFModuleWrapper(
      _sys.modules[__name__], "keras.models", public_apis=None, deprecation=True,
      has_lite=False)
