# Copyright 2019 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Strategy and optimizer combinations for combinations.combine()."""

import tensorflow.compat.v2 as tf

from tf_keras.src.distribute import simple_models

simple_functional_model = tf.__internal__.test.combinations.NamedObject(
    "SimpleFunctionalModel", simple_models.SimpleFunctionalModel()
)

simple_sequential_model = tf.__internal__.test.combinations.NamedObject(
    "SimpleSequentialModel", simple_models.SimpleSequentialModel()
)

simple_subclass_model = tf.__internal__.test.combinations.NamedObject(
    "SimpleSubclassModel", simple_models.SimpleSubclassModel()
)

simple_tfmodule_model = tf.__internal__.test.combinations.NamedObject(
    "SimpleTFModuleModel", simple_models.SimpleTFModuleModel()
)

