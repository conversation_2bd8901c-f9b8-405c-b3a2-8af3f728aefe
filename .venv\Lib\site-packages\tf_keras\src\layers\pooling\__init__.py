# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Keras Pooling layers."""


# Pooling layer aliases.
# Pooling layers.
from tf_keras.src.layers.pooling.average_pooling1d import AveragePooling1D
from tf_keras.src.layers.pooling.average_pooling1d import AvgPool1D
from tf_keras.src.layers.pooling.average_pooling2d import AveragePooling2D
from tf_keras.src.layers.pooling.average_pooling2d import AvgPool2D
from tf_keras.src.layers.pooling.average_pooling3d import AveragePooling3D
from tf_keras.src.layers.pooling.average_pooling3d import AvgPool3D
from tf_keras.src.layers.pooling.global_average_pooling1d import (
    GlobalAveragePooling1D,
)
from tf_keras.src.layers.pooling.global_average_pooling1d import GlobalAvgPool1D
from tf_keras.src.layers.pooling.global_average_pooling2d import (
    GlobalAveragePooling2D,
)
from tf_keras.src.layers.pooling.global_average_pooling2d import GlobalAvgPool2D
from tf_keras.src.layers.pooling.global_average_pooling3d import (
    GlobalAveragePooling3D,
)
from tf_keras.src.layers.pooling.global_average_pooling3d import GlobalAvgPool3D
from tf_keras.src.layers.pooling.global_max_pooling1d import GlobalMaxPool1D
from tf_keras.src.layers.pooling.global_max_pooling1d import GlobalMaxPooling1D
from tf_keras.src.layers.pooling.global_max_pooling2d import GlobalMaxPool2D
from tf_keras.src.layers.pooling.global_max_pooling2d import GlobalMaxPooling2D
from tf_keras.src.layers.pooling.global_max_pooling3d import GlobalMaxPool3D
from tf_keras.src.layers.pooling.global_max_pooling3d import GlobalMaxPooling3D
from tf_keras.src.layers.pooling.max_pooling1d import MaxPool1D
from tf_keras.src.layers.pooling.max_pooling1d import MaxPooling1D
from tf_keras.src.layers.pooling.max_pooling2d import MaxPool2D
from tf_keras.src.layers.pooling.max_pooling2d import MaxPooling2D
from tf_keras.src.layers.pooling.max_pooling3d import MaxPool3D
from tf_keras.src.layers.pooling.max_pooling3d import MaxPooling3D

