!pip install scikeras
!pip uninstall keras tensorflow tensorflow-cpu # or tensorflow-gpu if applicable
!pip install tensorflow # This will install Keras as part of TensorFlow
!pip install keras
%pip install tf_keras

import numpy as np
import tensorflow as tf
tf.random.set_seed(221)
import math
from pandas import read_csv
from keras.models import Sequential
from keras.layers import Dense,BatchNormalization, Activation
from keras import metrics
from scikeras.wrappers import KerasRegressor
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import r2_score
from sklearn.model_selection import cross_val_score, train_test_split, KFold
from tensorboard.plugins.hparams import api as hp
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from tensorflow.keras.optimizers import Adadelta,RMSprop,SGD,Adam,Nadam,Adagrad,Adamax, Ftrl
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import joblib
import pandas as pd

!pip install numpy==1.23.5
!pip install tensorflow==2.11.0
!pip install keras==2.11.0
!pip install scikit-learn==1.2.2
!pip install pandas==2.0.0
!pip install matplotlib==3.7.1
!pip install seaborn==0.12.2
!pip install joblib==1.2.0
!pip install pickle-mixin==1.0.2
!pip install -U tensorboard-plugin-hparams
!pip install -U keras-tuner
!pip install -U keras-autodocs  

#R2 coeff
def R2(y_true, y_pred):
    from keras import backend as K
    SS_res =  K.sum(K.square(y_true-y_pred))
    SS_tot = K.sum(K.square( y_true - K.mean(y_true) ) )
    return ( 1 - SS_res/(SS_tot + K.epsilon()) )

# load dataset
dataframe = read_csv('Comp_Prox_Ult.csv', encoding="utf-8")
#load validation data
val_dataframe = read_csv('/Users/<USER>/Desktop/4-1/biomass/validation_data.csv', encoding="utf-8")
train_df, test_df = train_test_split(dataframe, test_size = 0.2, random_state = 14)
dataset = dataframe.values
val_dataset = val_dataframe.values
val_X_data = val_dataset[:,0:13]
val_y_data = val_dataset[:,13:]
val_X_train = val_X_data[[0,2,4,5,8,9]]
val_y_train = val_y_data[[0,2,4,5,8,9]]
val_X_test = val_X_data[[1,3,6,7,10,11]]
val_y_test = val_y_data[[1,3,6,7,10,11]]
# split into input (X) and output (Y) variables
X_data = dataset[:,0:13]
y_data = dataset[:,13:]
val_X_data = val_dataset[:,0:13]
val_y_data = val_dataset[:,13:]

X_train, X_rem, y_train, y_rem = train_test_split(X_data, y_data, test_size = 0.2, random_state = 14)
X_test, X_val, y_test, y_val = train_test_split(X_rem, y_rem, test_size = 0.5, random_state = 19)


X_train = np.append(X_train,[
 [6.33,77.62,5.28,17.10,43.21,6.34,0.25,50.02,6.73,23.23,2.09,300.00,10.00],
 [6.33,77.62,5.28,17.10,43.21,6.34,0.25,50.02,6.73,23.23,2.09,350.00,10.00],
 [10.30,74.85,7.64,15.21,38.21,6.15,0.47,55.12,34.96,0.00,2.87,300.00,10.00],
 [10.30,74.85,7.64,15.21,38.21,6.15,0.47,55.12,34.96,0.00,2.87,350.00,10.00]
,[9.11,72.68,14.11,13.21,41.73,6.40,0.56,50.40,43.51,16.45,5.56,500.00,150.00],
[1.55,88.74,0.04,11.22,50.70,6.20,0.50,42.60,50.10,10.81,31.69,450.00,150.00]],axis=0)
y_train = np.append(y_train,[[35.90,41.00,23.10],
 [41.70,36.00,22.30],
 [29.10,33.50,37.40],
 [23.60,30.90,45.50]
,[37.08,32.35,30.56],
[54.50,15.30,30.20]],axis=0)


X_data = np.append(X_data,[
 [6.33,77.62,5.28,17.10,43.21,6.34,0.25,50.02,6.73,23.23,2.09,300.00,10.00],
 [6.33,77.62,5.28,17.10,43.21,6.34,0.25,50.02,6.73,23.23,2.09,350.00,10.00],
 [10.30,74.85,7.64,15.21,38.21,6.15,0.47,55.12,34.96,0.00,2.87,300.00,10.00],
 [10.30,74.85,7.64,15.21,38.21,6.15,0.47,55.12,34.96,0.00,2.87,350.00,10.00]
,[9.11,72.68,14.11,13.21,41.73,6.40,0.56,50.40,43.51,16.45,5.56,500.00,150.00],
[1.55,88.74,0.04,11.22,50.70,6.20,0.50,42.60,50.10,10.81,31.69,450.00,150.00]],axis=0)

y_data = np.append(y_data,[[35.90,41.00,23.10],
 [41.70,36.00,22.30],
 [29.10,33.50,37.40],
 [23.60,30.90,45.50]
,[37.08,32.35,30.56],
[54.50,15.30,30.20]],axis=0)


# Separating the outputs.
val_oil_train = val_y_train[:, 0]
val_char_train = val_y_train[:, 1]
val_gas_train = val_y_train[:, 2]

val_oil_test = val_y_test[:, 0]
val_char_test = val_y_test[:, 1]
val_gas_test = val_y_test[:, 2]

oil = y_data[:, 0]
char = y_data[:, 1]
gas = y_data[:, 2]

val_oil = val_y_data[:, 0]
val_char = val_y_data[:, 1]
val_gas = val_y_data[:, 2]

bio_oil_train = y_train[:, 0]
char_train = y_train[:, 1]
gas_train = y_train[:, 2]

bio_oil_val = y_val[:, 0]
bio_oil_test = y_test[:, 0]
char_val = y_val[: , 1]
char_test = y_test[:, 1]
gas_val = y_val[:, 2]
gas_test = y_test[:, 2]

oil_rem = y_rem[:, 0]
char_rem = y_rem[:, 1]
gas_rem = y_rem[:, 2]


# Min Max Scaling
from sklearn.preprocessing import MinMaxScaler
mms = MinMaxScaler()
X_train = mms.fit_transform(X_train)
# X_val = mms.transform(X_val)
X_rem = mms.transform(X_rem)
val_X_data_transformed = mms.transform(val_X_data)
val_X_train_transformed = mms.transform(val_X_train)
val_X_test_transformed = mms.transform(val_X_test)

mms_oil = MinMaxScaler()
oil_train = mms_oil.fit_transform(bio_oil_train.reshape(-1,1))
bio_oil_test = mms_oil.transform(oil_rem.reshape(-1,1))
val_oil_train_transformed = mms_oil.transform(val_oil_train.reshape(-1,1))
val_oil_test_transformed = mms_oil.transform(val_oil_test.reshape(-1,1))

mms_char = MinMaxScaler()
char_train = mms_char.fit_transform(char_train.reshape(-1,1))
char_test = mms_char.transform(char_rem.reshape(-1,1))
val_char_train_transformed = mms_char.transform(val_char_train.reshape(-1,1))
val_char_test_transformed = mms_char.transform(val_char_test.reshape(-1,1))

mms_gas = MinMaxScaler()
gas_train = mms_gas.fit_transform(gas_train.reshape(-1,1))
gas_test = mms_gas.transform(gas_rem.reshape(-1,1))
val_gas_train_transformed = mms_gas.transform(val_gas_train.reshape(-1,1))
val_gas_test_transformed = mms_gas.transform(val_gas_test.reshape(-1,1))


from sklearn.ensemble import RandomForestRegressor
gb_oil = RandomForestRegressor()
gb_oil.fit(X_train, oil_train.reshape(-1,))

y_pred_test = gb_oil.predict(X_rem)
y_pred_test = mms_oil.inverse_transform(y_pred_test.reshape(-1,1))
# bio_oil_test = mms_oil.inverse_transform(bio_oil_test.reshape(-1,1))
error = mean_absolute_error(oil_rem, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(oil_rem, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = gb_oil.predict(val_X_test_transformed)
y_pred_val_test = mms_oil.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_oil_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_oil_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(gb_oil, 'rf_oil_random.sav')

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(oil_rem, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = oil_rem
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Bio-oil", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_oil_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-oil", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

from sklearn.ensemble import RandomForestRegressor
gb_char = RandomForestRegressor()
gb_char.fit(X_train, char_train.reshape(-1,))

y_pred_test = gb_char.predict(X_rem)
y_pred_test = mms_char.inverse_transform(y_pred_test.reshape(-1,1))
# char_test = mms_char.inverse_transform(char_test.reshape(-1,1))
error = mean_absolute_error(char_rem, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(char_rem, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = gb_char.predict(val_X_test_transformed)
y_pred_val_test = mms_char.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_char_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_char_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(gb_char, 'rf_char_random.sav')

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(char_rem, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = char_rem
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Bio-char", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_char_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-char", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

from sklearn.ensemble import RandomForestRegressor
gb_gas = RandomForestRegressor()
gb_gas.fit(X_train, gas_train.reshape(-1,))

y_pred_test = gb_gas.predict(X_rem)
y_pred_test = mms_gas.inverse_transform(y_pred_test.reshape(-1,1))
gas_test1 = mms_gas.inverse_transform(gas_test.reshape(-1,1))
error = mean_absolute_error(gas_test1, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(gas_test1, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = gb_gas.predict(val_X_test_transformed)
y_pred_val_test = mms_gas.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_gas_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_gas_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(gb_gas, 'rf_gas_random.sav')

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(gas_test1, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = gas_test1
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Bio-gas", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_gas_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-gas", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.ensemble import RandomForestRegressor
param_grid = {
    'n_estimators': [50, 100, 250, 500, 750, 1000],
    'max_depth' : list(range(1, 10)),
    'min_samples_leaf' : list(range(1,10)),
    'max_features' : [1.0, 'sqrt'],
    'bootstrap' : [True, False],
    'min_samples_split': list(range(2,10))
}

char_grid = RandomForestRegressor()

# rf_random_cv = RandomizedSearchCV(estimator = rf_grid, param_distributions= param_grid, random_state = 30, n_iter = 500)
char_grid_cv = GridSearchCV(estimator = char_grid, param_grid = param_grid, n_jobs = -1, verbose = 0, cv = 3)
char_grid_cv.fit(X_train.reshape(-1,13), char_train.reshape(-1,))

y_pred_test = char_grid_cv.predict(X_rem)
y_pred_test = mms_char.inverse_transform(y_pred_test.reshape(-1,1))
# char_test = mms_char.inverse_transform(char_test.reshape(-1,1))
error = mean_absolute_error(char_rem, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(char_rem, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = char_grid_cv.predict(val_X_test_transformed)
y_pred_val_test = mms_char.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_char_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_char_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(char_grid_cv, 'rf_char_grid_cv_final.sav')
model_char = joblib.load('rf_char_grid_cv_final.sav')
model_char.best_params_

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(char_rem, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = char_rem
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Char", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_char_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-char", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

from sklearn.model_selection import GridSearchCV, RandomizedSearchCV

param_grid = {
    'n_estimators': [50, 100, 250, 500, 750, 1000],
    'max_depth' : list(range(1, 10)),
    'min_samples_leaf' : list(range(1,10)),
    'max_features' : [1.0, 'sqrt'],
    'bootstrap' : [True, False],
    'min_samples_split': list(range(2,10))
}

oil_grid = RandomForestRegressor()

# rf_random_cv = RandomizedSearchCV(estimator = rf_grid, param_distributions= param_grid, random_state = 30, n_iter = 500)
oil_grid_cv = GridSearchCV(estimator = oil_grid, param_grid = param_grid, n_jobs = -1, verbose = 0, cv = 3)
oil_grid_cv.fit(X_train, oil_train.reshape(-1,))

y_pred_test = oil_grid_cv.predict(X_rem)
y_pred_test = mms_oil.inverse_transform(y_pred_test.reshape(-1,1))
# bio_oil_test = mms_oil.inverse_transform(bio_oil_test.reshape(-1,1))
error = mean_absolute_error(oil_rem, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(oil_rem, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = oil_grid_cv.predict(val_X_test_transformed)
y_pred_val_test = mms_oil.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_oil_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_oil_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(oil_grid_cv, 'rf_oil_grid_cv_final.sav')

model_oil = joblib.load('rf_oil_grid_cv_final.sav')
model_oil.best_params_

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(oil_rem, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = oil_rem
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Bio-Oil", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_oil_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-oil", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

from sklearn.model_selection import GridSearchCV, RandomizedSearchCV

param_grid = {
    'n_estimators': [50, 100, 250, 500, 750, 1000],
    'max_depth' : list(range(1, 10)),
    'min_samples_leaf' : list(range(1,10)),
    'max_features' : [1.0, 'sqrt'],
    'bootstrap' : [True, False],
    'min_samples_split': list(range(2,10))
}

gas_grid = RandomForestRegressor()

# rf_random_cv = RandomizedSearchCV(estimator = rf_grid, param_distributions= param_grid, random_state = 30, n_iter = 500)
gas_grid_cv = GridSearchCV(estimator = gas_grid, param_grid = param_grid, n_jobs = -1, verbose = 0, cv = 3)
gas_grid_cv.fit(X_train, gas_train.reshape(-1,))

y_pred_test = gas_grid_cv.predict(X_rem)
y_pred_test = mms_gas.inverse_transform(y_pred_test.reshape(-1,1))
# bio_oil_test = mms_oil.inverse_transform(bio_oil_test.reshape(-1,1))
error = mean_absolute_error(gas_rem, y_pred_test)
print('MAE: %.3f' % error)
R2_score = r2_score(gas_rem, y_pred_test)
print('R^2 Score: %.3f' % R2_score)
y_pred_val_test = gas_grid_cv.predict(val_X_test_transformed)
y_pred_val_test = mms_gas.inverse_transform(y_pred_val_test.reshape(-1,1))
# bio_val_oil_test = mms_oil.inverse_transform(bio_val_oil_test.reshape(-1,1))
error = mean_absolute_error(val_gas_test, y_pred_val_test)
print('MAE (val): %.3f' % error)
R2_score = r2_score(val_gas_test, y_pred_val_test)
print('R^2 Score (val): %.3f' % R2_score)
joblib.dump(gas_grid_cv, 'rf_gas_grid_cv_final.sav')

model_gas = joblib.load('rf_gas_grid_cv_final.sav')
model_gas.best_params_

import matplotlib.pyplot as plt
font_axis_title = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 18,
        }
font_axis_subhead = {
        'color':  'black',
        # 'weight': 'bold',
        'size': 14,
        }
plt.rcParams['ytick.labelsize'] = 16
plt.rcParams['xtick.labelsize'] = 16

plt.plot(gas_rem, color = 'red', label = 'Real data')
plt.plot(y_pred_test, color = 'blue', label = 'Predicted data')
plt.title('Prediction')
plt.legend()
plt.show()

fignow = plt.figure(figsize=(6,6))
x = gas_rem
y = y_pred_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot: Gas", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()


fignow = plt.figure(figsize=(6,6))
x = val_gas_test
y = y_pred_val_test
bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
ax = plt.gca()
ax.set_xlim(bounds)
ax.set_ylim(bounds)
ax.set_aspect("equal", adjustable="box")
plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

mean_abs_err = np.mean(np.abs(x-y))
rmse = np.sqrt(np.mean((x-y)**2))
rmse_std = rmse / np.std(y)
#z = np.polyfit(x,y, 1)
#y_hat = np.poly1d(z)(x)

text = f"$R^2 = {r2_score(x,y):0.3f}$"

plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
     fontsize=14, verticalalignment='top')

plt.title("Parity Plot (val): Bio-gas", fontdict=font_axis_subhead)
plt.xlabel('Actual Values', fontdict=font_axis_subhead)
plt.ylabel('Predicted Values', fontdict=font_axis_subhead)
plt.show()

def load_model(x_train,x_test,val_x_train,val_x_test,y_train, y_test_,val_y_train,val_y_test,mms,mms_test,model_name,name):
  model = joblib.load(model_name)
   # Train Test Data Prediction
  y_pred_train = model.predict(x_train)
  y_pred_train = mms_test.inverse_transform(y_pred_train.reshape(-1,1))
  y_pred_test = model.predict(x_test)
  y_pred_test = mms_test.inverse_transform(y_pred_test.reshape(-1,1))

  # Validation train test Data Prediction
  val_y_pred_train = model.predict(val_x_train)
  val_y_pred_train = mms_test.inverse_transform(val_y_pred_train.reshape(-1,1))
  val_y_pred_test = model.predict(val_x_test)
  val_y_pred_test = mms_test.inverse_transform(val_y_pred_test.reshape(-1,1))

  # Inverse Transform Train Test Data using MMS
  x_train = mms.inverse_transform(x_train)
  x_test = mms.inverse_transform(x_test)
  y_train = mms_test.inverse_transform(y_train)
  y_test_ = mms_test.inverse_transform(y_test_.reshape(-1,1))

  # Inverse Transform Validation Train Test Data using MMS
  val_x_train = mms.inverse_transform(val_x_train)
  val_x_test = mms.inverse_transform(val_x_test)
  val_y_train = mms_test.inverse_transform(val_y_train)
  val_y_test = mms_test.inverse_transform(val_y_test)

  error = mean_absolute_error(y_train, y_pred_train,multioutput='uniform_average')
  print('MAE: %.3f' % error)
  R2_score = r2_score(y_train, y_pred_train)
  print('R^2 Score: %.3f' %R2_score)

  error = mean_absolute_error(y_test_, y_pred_test,multioutput='uniform_average')
  print('MAE: %.3f' % error)
  R2_score = r2_score(y_test_, y_pred_test)
  print('R^2 Score: %.3f' % R2_score)

  error = mean_absolute_error(val_y_test, val_y_pred_test,multioutput='uniform_average')
  print('MAE: %.3f' % error)
  R2_score = r2_score(val_y_test,val_y_pred_test)
  print('R^2 Score: %.3f' % R2_score)


  train_data = np.append(x_train,y_train,axis=1)
  train_data = np.append(train_data,y_pred_train,axis=1)
  test_data = np.append(x_test,y_test_,axis=1)
  test_data = np.append(test_data,y_pred_test,axis=1)
  myTrainData = pd.DataFrame(train_data)
  myTrainData.columns = ['Moisture',	'Vol. Matter',	'Ash',	'Fixed Carbon',	'C'	,'H'	,'N',	'O',	'Cellulose',	'Hemicellulose',	'Lignin',	'Exit/Final Temperature ((ºC)'	,'Heating Rate (ºC/min)'	,'Actual Bio-Gas Yield',f'Predicted {name} Yield']#'Actual Bio-oil Yield'	,'Actual Bio-char Yield'	,,'Predicted Bio-oil Yield'	,'Predicted Bio-char Yield'	,'Predicted Gas Yield']
  myTestData = pd.DataFrame(test_data)
  myTestData.columns = ['Moisture',	'Vol. Matter',	'Ash',	'Fixed Carbon',	'C'	,'H'	,'N',	'O',	'Cellulose',	'Hemicellulose',	'Lignin',	'Exit/Final Temperature ((ºC)'	,'Heating Rate (ºC/min)'	,'Actual Bio-Gas Yield',f'Predicted {name} Yield']#'Actual Bio-oil Yield'	,'Actual Bio-char Yield'	,,'Predicted Bio-oil Yield'	,'Predicted Bio-char Yield'	,'Predicted Gas Yield']
  myTrainData.to_csv(f"./files/train_data_{name}.csv",index=False)
  myTestData.to_csv(f"./files/test_data_{name}.csv",index=False)
  myCompleteData = myTrainData.append(myTestData)
  myCompleteData.to_csv(f"./files/all_data_{name}.csv",index=False)

  # Validation Train Test Data
  val_train_data = np.append(val_x_train,val_y_train,axis=1)
  val_train_data = np.append(val_train_data,val_y_pred_train,axis=1)
  val_test_data = np.append(val_x_test,val_y_test,axis=1)
  val_test_data = np.append(val_test_data,val_y_pred_test,axis=1)
  val_myTrainData = pd.DataFrame(val_train_data)
  val_myTrainData.columns = ['Moisture',	'Vol. Matter',	'Ash',	'Fixed Carbon',	'C'	,'H'	,'N',	'O',	'Cellulose',	'Hemicellulose',	'Lignin',	'Exit/Final Temperature ((ºC)'	,'Heating Rate (ºC/min)'	,f'Actual Bio-{name} Yield',f'Predicted Bio-{name} Yield']#'Actual Bio-oil Yield'	,'Actual Bio-char Yield'	,,'Predicted Bio-oil Yield'	,'Predicted Bio-char Yield'	,'Predicted Gas Yield']
  val_myTestData = pd.DataFrame(val_test_data)
  val_myTestData.columns = ['Moisture',	'Vol. Matter',	'Ash',	'Fixed Carbon',	'C'	,'H'	,'N',	'O',	'Cellulose',	'Hemicellulose',	'Lignin',	'Exit/Final Temperature ((ºC)'	,'Heating Rate (ºC/min)'	,f'Actual Bio-{name} Yield',f'Predicted Bio-{name} Yield']#'Actual Bio-oil Yield'	,'Actual Bio-char Yield'	,,'Predicted Bio-oil Yield'	,'Predicted Bio-char Yield'	,'Predicted Gas Yield']
  val_myTrainData.to_csv(f"./files/val_train_data_{name}.csv",index=False)
  val_myTestData.to_csv(f"./files/val_test_data_{name}.csv",index=False)

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,gas_train,gas_test,val_gas_train_transformed,val_gas_test_transformed,mms,mms_gas,'rf_gas_grid_cv_final.sav','Bio-Gas')

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,char_train,char_test,val_char_train_transformed,val_char_test_transformed,mms,mms_char,'rf_char_grid_cv_final.sav','Bio-Char')
# load_model(X_train,X_rem,char_train,char_test,mms,mms_char,'gbr_char_grid_cv_final.sav','Bio-Char')

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,oil_train,bio_oil_test,val_oil_train_transformed,val_oil_test_transformed,mms,mms_oil,'rf_oil_grid_cv_final.sav','Bio-Oil')
# load_model(X_train,X_rem,oil_train,bio_oil_test,mms,mms_oil,'gbr_oil_grid_cv_final.sav','Bio-Oil')

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,char_train,char_test,val_char_train_transformed,val_char_test_transformed,mms,mms_char,'rf_char_random.sav','Bio-Char_random')
# load_model(X_train,X_rem,char_train,char_test,mms,mms_char,'gbr_char_random.sav','Bio-Char')

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,oil_train,bio_oil_test,val_oil_train_transformed,val_oil_test_transformed,mms,mms_oil,'rf_oil_random.sav','Bio-Oil_random')
# load_model(X_train,X_rem,oil_train,bio_oil_test,mms,mms_oil,'gbr_oil_random.sav','Bio-Oil')

load_model(X_train,X_rem,val_X_train_transformed,val_X_test_transformed,gas_train,gas_test,val_gas_train_transformed,val_gas_test_transformed,mms,mms_gas,'rf_gas_random.sav','Bio-Gas_random')
# load_model(X_train,X_rem,gas_train,gas_test,mms,mms_gas,'gbr_gas_random.sav','Bio-Gas')

def getR2(x_train,y_train,mms,mms_test,model_name):
  model = joblib.load(model_name)
  # model.summary()
  y_pred_train = model.predict(x_train)
  x_train = mms.inverse_transform(x_train)
  y_train = mms_test.inverse_transform(y_train.reshape(-1,1))
  y_pred_train = mms_test.inverse_transform(y_pred_train.reshape(-1,1))
  R2_score = r2_score(y_train, y_pred_train)
  print(R2_score)

getR2(mms.transform(X_data),mms_char.transform(char.reshape(-1, 1)),mms,mms_char,'rf_char_random.sav')
getR2(X_train,char_train,mms,mms_char,'rf_char_random.sav')
getR2(X_rem,char_test,mms,mms_char,'rf_char_random.sav')
getR2(val_X_train_transformed,val_char_train_transformed,mms,mms_char,'rf_char_random.sav')
getR2(val_X_test_transformed,val_char_test_transformed,mms,mms_char,'rf_char_random.sav')

getR2(mms.transform(X_data),mms_oil.transform(oil.reshape(-1, 1)),mms,mms_oil,'rf_oil_random.sav')
getR2(X_train,oil_train,mms,mms_oil,'rf_oil_random.sav')
getR2(X_rem,bio_oil_test,mms,mms_oil,'rf_oil_random.sav')
getR2(val_X_train_transformed,val_oil_train_transformed,mms,mms_oil,'rf_oil_random.sav')
getR2(val_X_test_transformed,val_oil_test_transformed,mms,mms_oil,'rf_oil_random.sav')

getR2(mms.transform(X_data),mms_gas.transform(gas.reshape(-1, 1)),mms,mms_gas,'rf_gas_random.sav')
getR2(X_train,gas_train,mms,mms_gas,'rf_gas_random.sav')
getR2(X_rem,gas_test,mms,mms_gas,'rf_gas_random.sav')
getR2(val_X_train_transformed,val_gas_train_transformed,mms,mms_gas,'rf_gas_random.sav')
getR2(val_X_test_transformed,val_gas_test_transformed,mms,mms_gas,'rf_gas_random.sav')

getR2(mms.transform(X_data),mms_char.transform(char.reshape(-1, 1)),mms,mms_char,'rf_char_grid_cv_final.sav')
getR2(X_train,char_train,mms,mms_char,'rf_char_grid_cv_final.sav')
getR2(X_rem,char_test,mms,mms_char,'rf_char_grid_cv_final.sav')
getR2(val_X_train_transformed,val_char_train_transformed,mms,mms_char,'rf_char_grid_cv_final.sav')
getR2(val_X_test_transformed,val_char_test_transformed,mms,mms_char,'rf_char_grid_cv_final.sav')

getR2(mms.transform(X_data),mms_oil.transform(oil.reshape(-1, 1)),mms,mms_oil,'rf_oil_grid_cv_final.sav')
getR2(X_train,oil_train,mms,mms_oil,'rf_oil_grid_cv_final.sav')
getR2(X_rem,bio_oil_test,mms,mms_oil,'rf_oil_grid_cv_final.sav')
getR2(val_X_train_transformed,val_oil_train_transformed,mms,mms_oil,'rf_oil_grid_cv_final.sav')
getR2(val_X_test_transformed,val_oil_test_transformed,mms,mms_oil,'rf_oil_grid_cv_final.sav')

getR2(mms.transform(X_data),mms_gas.transform(gas.reshape(-1, 1)),mms,mms_gas,'rf_gas_grid_cv_final.sav')
getR2(X_train,gas_train,mms,mms_gas,'rf_gas_grid_cv_final.sav')
getR2(X_rem,gas_test,mms,mms_gas,'rf_gas_grid_cv_final.sav')
getR2(val_X_train_transformed,val_gas_train_transformed,mms,mms_gas,'rf_gas_grid_cv_final.sav')
getR2(val_X_test_transformed,val_gas_test_transformed,mms,mms_gas,'rf_gas_grid_cv_final.sav')

import matplotlib.pyplot as plt
def val_model(val_x,val_y,mms_train,mms,model_name,name):
  model = joblib.load(model_name)
  y_pred_val = model.predict(val_x)
  y_pred_val_transformed = mms.inverse_transform(y_pred_val.reshape(-1, 1))
  val_x_transformed = mms_train.inverse_transform(val_x)
  val_y_transformed = mms.inverse_transform(val_y.reshape(-1, 1))
  error = mean_absolute_error(val_y_transformed, y_pred_val_transformed,multioutput='uniform_average')
  print('MAE: %.3f' % error)
  R2_score = r2_score(val_y_transformed, y_pred_val_transformed)
  print('R^2 Score: %.3f' %R2_score)
  val_data = np.append(val_x_transformed,val_y_transformed,axis=1)
  val_data = np.append(val_data,y_pred_val_transformed,axis=1)
  myValData = pd.DataFrame(val_data)
  myValData.columns = ['Moisture',	'Vol. Matter',	'Ash',	'Fixed Carbon',	'C'	,'H'	,'N',	'O',	'Cellulose',	'Hemicellulose',	'Lignin',	'Exit/Final Temperature ((ºC)'	,'Heating Rate (ºC/min)'	,f'Actual {name} Yield',f'Predicted {name} Yield']#'Actual Bio-oil Yield'	,'Actual Bio-char Yield'	,,'Predicted Bio-oil Yield'	,'Predicted Bio-char Yield'	,'Predicted Gas Yield']
  myValData.to_csv(f"./val_data_{name}.csv",index=False)
  font_axis_title = {
          'color':  'black',
          # 'weight': 'bold',
          'size': 18,
          }
  font_axis_subhead = {
          'color':  'black',
          # 'weight': 'bold',
          'size': 14,
          }
  plt.rcParams['ytick.labelsize'] = 16
  plt.rcParams['xtick.labelsize'] = 16

  plt.plot(val_y_transformed, color = 'red', label = 'Real data')
  plt.plot(y_pred_val_transformed, color = 'blue', label = 'Predicted data')
  plt.title('Prediction')
  plt.legend()
  plt.show()

  fignow = plt.figure(figsize=(6,6))
  x = val_y_transformed
  y = y_pred_val_transformed
  bounds = (min(x.min(), y.min()) - int(0.1 * y.min()), max(x.max(), y.max())+ int(0.1 * y.max()))
  ax = plt.gca()
  ax.set_xlim(bounds)
  ax.set_ylim(bounds)
  ax.set_aspect("equal", adjustable="box")
  plt.plot(x,y,"o", alpha=0.8 ,ms=10, markeredgewidth=0.0)
  ax.plot([0, 1], [0, 1], "r-",lw=2 ,transform=ax.transAxes)

  mean_abs_err = np.mean(np.abs(x-y))
  rmse = np.sqrt(np.mean((x-y)**2))
  rmse_std = rmse / np.std(y)
  #z = np.polyfit(x,y, 1)
  #y_hat = np.poly1d(z)(x)

  text = f"$R^2 = {r2_score(x,y):0.3f}$"

  plt.gca().text(0.05, 0.95, text,transform=plt.gca().transAxes,
      fontsize=14, verticalalignment='top')

  plt.title(f"Parity Plot: {name}", fontdict=font_axis_subhead)
  plt.xlabel('Actual Values', fontdict=font_axis_subhead)
  plt.ylabel('Predicted Values', fontdict=font_axis_subhead)

val_model(val_X_data_transformed,val_gas_test,mms,mms_gas,'gbr_gas_grid_cv_final.sav','Bio-Gas')

val_model(val_X_data_transformed,val_char_test,mms,mms_char,'gbr_char_grid_cv_final.sav','Bio-Char')

val_model(val_X_data_transformed,val_oil_test,mms,mms_oil,'gbr_oil_grid_cv_final.sav','Bio-oil')

import joblib
model_char = joblib.load('RF with exp train/Model Weights/rf_gas_grid_cv_final.sav')
model_char.best_params_


# or if using venv + pyenv
from gettext import install


!pyenv install 3.10.14
!pyenv virtualenv 3.10.14 py310_env
!pyenv activate py310_env
